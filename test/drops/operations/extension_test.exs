defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Support.{PrepareExtension, ValidateExtension, ManualExtension}

  describe "extension registration" do
    test "register_extension/1 adds new extensions to registry" do
      defmodule Test.MyOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
        register_extension(ManualExtension)
      end

      assert Drops.Operations.registered_extensions() == []

      assert Test.MyOperations.registered_extensions() == [
               PrepareExtension,
               ManualExtension
             ]
    end

    test "operations inherit extensions from base module" do
      defmodule Test.MyOperationsWithExtensions do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.MyOperation do
        use Test.MyOperationsWithExtensions

        def call(context) do
          {:ok, context}
        end
      end

      assert Test.MyOperationsWithExtensions.registered_extensions() == [PrepareExtension]
    end
  end

  describe "extension behavior verification" do
    test "debug extension loading" do
      defmodule Test.DebugOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.DebugOperation do
        use Test.DebugOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Debug: Check what extensions are registered
      IO.inspect(Test.DebugOperations.registered_extensions(),
        label: "Registered extensions"
      )

      # Debug: Check if the function exists
      functions = Test.DebugOperation.__info__(:functions)
      IO.inspect(functions, label: "Available functions")

      # Check if extension loaded function exists
      has_extension_loaded =
        Enum.any?(functions, fn {name, _arity} ->
          name == :__prepare_extension_loaded?
        end)

      IO.inspect(has_extension_loaded, label: "Has extension loaded function")
    end

    test "PrepareExtension modifies params in prepare step" do
      defmodule Test.PrepareOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.PrepareOperation do
        use Test.PrepareOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Test that prepare extension modifies the name
      {:ok, result} = Test.PrepareOperation.call(%{params: %{name: "test"}})
      assert result == %{name: "prepared_test"}
    end

    test "ValidateExtension adds custom validation" do
      defmodule Test.ValidateOperations do
        use Drops.Operations

        register_extension(ValidateExtension)
      end

      defmodule Test.ValidateOperation do
        use Test.ValidateOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Verify the extension is loaded
      assert Test.ValidateOperation.__validate_extension_loaded?()

      # Test successful validation
      {:ok, result} = Test.ValidateOperation.call(%{params: %{name: "valid_name"}})
      assert result == %{name: "valid_name"}

      # Test failed validation
      {:error, error} = Test.ValidateOperation.call(%{params: %{name: "invalid_name"}})
      assert error == "name cannot contain 'invalid'"
    end

    test "multiple extensions work together" do
      defmodule Test.MultiExtensionOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
        register_extension(ValidateExtension)
      end

      defmodule Test.MultiExtensionOperation do
        use Test.MultiExtensionOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Verify both extensions are loaded
      assert Test.MultiExtensionOperation.__prepare_extension_loaded?()
      assert Test.MultiExtensionOperation.__validate_extension_loaded?()

      # Test that both extensions work together
      # PrepareExtension adds "prepared_" prefix, ValidateExtension checks for "invalid"
      {:ok, result} = Test.MultiExtensionOperation.call(%{params: %{name: "test"}})
      assert result == %{name: "prepared_test"}

      # Test that validation fails even with prepare modification
      {:error, error} = Test.MultiExtensionOperation.call(%{params: %{name: "invalid"}})
      assert error == "name cannot contain 'invalid'"
    end
  end
end
