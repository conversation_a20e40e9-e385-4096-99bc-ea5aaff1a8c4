defmodule Test.Support.PrepareExtension do
  @moduledoc """
  A test extension that provides custom prepare functionality.

  This extension modifies the prepare step to add a prefix to the name parameter.
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :prepare_extension)
  end

  @impl true
  def extend_using_macro(opts) do
    quote do
      # Add a module attribute to track that this extension was loaded
      @prepare_extension_loaded true

      # Define a function to check if the extension is loaded
      def __prepare_extension_loaded?, do: @prepare_extension_loaded
    end
  end

  @impl true
  def extend_operation_runtime(opts) do
    quote do
      # Override prepare to add prefix to name
      def prepare(%{params: params} = context) do
        updated_params =
          if Map.has_key?(params, :name) do
            Map.put(params, :name, "prepared_" <> params.name)
          else
            params
          end

        {:ok, Map.put(context, :params, updated_params)}
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _opts) do
    uow
  end
end

defmodule Test.Support.ValidateExtension do
  @moduledoc """
  A test extension that provides custom validate functionality.

  This extension adds validation to ensure the name parameter doesn't contain "invalid".
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :validate_extension)
  end

  @impl true
  def extend_using_macro(opts) do
    quote do
      # Add a module attribute to track that this extension was loaded
      @validate_extension_loaded true

      # Define a function to check if the extension is loaded
      def __validate_extension_loaded?, do: @validate_extension_loaded
    end
  end

  @impl true
  def extend_operation_runtime(opts) do
    quote do
      # Override validate to add custom validation
      def validate(%{params: params} = context) do
        if Map.has_key?(params, :name) and String.contains?(params.name, "invalid") do
          {:error, "name cannot contain 'invalid'"}
        else
          {:ok, context}
        end
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _opts) do
    uow
  end
end
